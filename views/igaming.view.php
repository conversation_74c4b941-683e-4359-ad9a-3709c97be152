<?php
#region region DOCS
/** @var Gaming $newgaming */
/** @var Platform[] $platforms */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | Gaming</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #region region CSS select choices.js  ?>
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/choices.min.css">
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/fab_choices.css">
	<?php #endregion CSS select choices.js  ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Crear juego</h4>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region FORM ?>
		<form action="igaming" method="POST">
			<!-- BEGIN row -->
			<div class="row mt-3">
				<!-- BEGIN text -->
				<div class="col-md-6 col-xs-12">
					<label class="form-label">Nombre:</label>
					<input type="text" name="nombre" id="nombre" value="<?php echo @recover_var($newgaming->name) ?>" class="form-control" autofocus/>
				</div>
				<!-- END text -->
				<!-- BEGIN select -->
				<div class="col-md-6 col-xs-12">
					<label class="form-label">Plataforma:</label>
					<select id="platform" name="platform" class="form-select form-select-choices">
						<option value="">--</option>
						
						<?php foreach ($platforms as $platform): ?>
							<option <?php @recover_var_list(ordena($newgaming->id_platform), ordena($platform->id_platform)) ?> value="<?php echo limpiar_datos($platform->id_platform); ?>">
								<?php echo $platform->name; ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>
				<!-- END select -->
			</div>
			<!-- END row -->
			<!-- BEGIN row -->
			<div class="row mt-3">
				<div class="col-md-8 col-xs-12">
					<label class="form-label mb-2">Estado de instalación: <span class="text-danger">*</span></label>

					<!-- Radio button group (2 options only) -->
					<div class="mb-3">
						<?php
						// Determine current installation status for radio button selection
						$current_status = '';
						if ($newgaming->por_instalar == 1) {
							$current_status = 'por_instalar';
						} else {
							// For new records (empty id_gaming), default to "por_instalar"
							// For existing records with por_instalar = 0, show "instalado"
							$current_status = empty($newgaming->id_gaming) ? 'por_instalar' : 'instalado';
						}
						?>
						<div class="form-check mb-2">
							<input class="form-check-input" type="radio" name="installation_status" id="radio_por_instalar" value="por_instalar"
							       <?php recoverradio($current_status, 'por_instalar'); ?> required>
							<label class="form-check-label" for="radio_por_instalar">
								Por instalar
							</label>
						</div>
						<div class="form-check mb-2">
							<input class="form-check-input" type="radio" name="installation_status" id="radio_instalado" value="instalado"
							       <?php recoverradio($current_status, 'instalado'); ?> required>
							<label class="form-check-label" for="radio_instalado">
								Instalado
							</label>
						</div>
					</div>

					<!-- "Listo para instalar" switch - only visible when "Por instalar" is selected -->
					<div id="listo_para_instalar_container" class="mb-3" style="display: none;">
						<div class="form-check form-switch">
							<input type="checkbox" id="listo_para_instalar" name="listo_para_instalar"
							       <?php echo @recoverVarCheckbox($newgaming->listo_para_instalar); ?> class="form-check-input">
							<label class="form-check-label fs-12px" for="listo_para_instalar">
								Listo para instalar
							</label>
						</div>
					</div>

					<!-- Text field for "Por instalar" option -->
					<div id="texto_por_instalar_container" class="mt-2" style="display: none;">
						<label for="texto_por_instalar" class="form-label">Nota por instalar: <span class="text-danger">*</span></label>
						<input type="text" class="form-control" id="texto_por_instalar" name="texto_por_instalar"
						       value="<?php echo htmlspecialchars(@recover_var($newgaming->texto_por_instalar) ?? '', ENT_QUOTES); ?>"
						       aria-label="Texto de por instalar">
					</div>
				</div>
			</div>
			<!-- END row -->
			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region SUBMIT sub_add ?>
				<div class="col-md-12 col-xs-12">
					<button type="submit" id="sub_add" name="sub_add" class="btn btn-md btn-success w-100">
						Agregar
					</button>
				</div>
				<?php #endregion SUBMIT sub_add ?>
			</div>
			<!-- END row -->
			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region LINK regresar ?>
				<div class="col-md-12 col-xs-12">
					<a href="agaming" class="btn btn-md btn-default w-100">
						Regresar
					</a>
				</div>
				<?php #endregion LINK regresar ?>
			</div>
			<!-- END row -->
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
	<script type="text/javascript">
        swal({
            text   : "<?php echo $success_text; ?>",
            icon   : 'success',
            buttons: {
                confirm: {
                    text      : 'Ok',
                    value     : true,
                    visible   : true,
                    className : 'btn btn-success',
                    closeModal: true
                }
            }
        });
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
	<script type="text/javascript">
        swal({
            text   : "<?php echo $error_text; ?>",
            icon   : 'error',
            buttons: {
                confirm: {
                    text      : 'Ok',
                    value     : true,
                    visible   : true,
                    className : 'btn btn-danger',
                    closeModal: true
                }
            }
        });
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region JS select choices.js ?>
<script src="<?php echo RUTA ?>resources/choices.js/choices.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const selectElements = document.querySelectorAll('.form-select-choices');
        
        selectElements.forEach((element) => {
            const choices = new Choices(element, {
                searchEnabled   : true, // Enable search functionality
                removeItemButton: true, // Allow removing selected items in multi-select
                shouldSort      : false // Disable sorting of options
            });
        });
    });
</script>
<?php #endregion JS select choices.js ?>

<?php #region region JS installation status radio buttons ?>
<script>
document.addEventListener('DOMContentLoaded', function () {
    const radioPorInstalar = document.getElementById('radio_por_instalar');
    const radioInstalado = document.getElementById('radio_instalado');
    const textoContainer = document.getElementById('texto_por_instalar_container');
    const textoInput = document.getElementById('texto_por_instalar');
    const listoContainer = document.getElementById('listo_para_instalar_container');
    const listoSwitch = document.getElementById('listo_para_instalar');
    const form = document.querySelector('form');

    // Function to toggle field visibility based on radio selection
    function toggleFieldVisibility() {
        if (radioPorInstalar.checked) {
            // Show text field and "listo para instalar" switch
            textoContainer.style.display = 'block';
            listoContainer.style.display = 'block';
            textoInput.required = true;
            listoSwitch.disabled = false;
        } else {
            // Hide both text field and switch, clear values
            textoContainer.style.display = 'none';
            listoContainer.style.display = 'none';
            textoInput.required = false;
            textoInput.value = '';
            listoSwitch.checked = false;
            listoSwitch.disabled = true;
        }
    }

    // Add event listeners to radio buttons
    radioPorInstalar.addEventListener('change', toggleFieldVisibility);
    radioInstalado.addEventListener('change', toggleFieldVisibility);

    // Initialize visibility on page load
    toggleFieldVisibility();

    // Form validation
    form.addEventListener('submit', function(e) {
        // Check if any radio button is selected
        const installationStatusSelected = document.querySelector('input[name="installation_status"]:checked');
        if (!installationStatusSelected) {
            e.preventDefault();
            alert('Debe seleccionar un estado de instalación.');
            return false;
        }

        // If "Por instalar" is selected, validate text field
        if (radioPorInstalar.checked) {
            const textoValue = textoInput.value.trim();
            if (textoValue === '') {
                e.preventDefault();
                alert('Debe especificar una nota cuando el juego está marcado como "Por instalar".');
                textoInput.focus();
                return false;
            }
        }
    });
});
</script>
<?php #endregion JS installation status radio buttons ?>
<?php #endregion JS ?>

</body>
</html>